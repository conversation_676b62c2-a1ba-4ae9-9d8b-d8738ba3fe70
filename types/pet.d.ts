export interface PetData {
  photo: string | null;
  name: string;
  species: string;
  breed: string;
  sex: string;
  weight: string;
  weightUnit: string;
  dob: Date | null;
  showDate?: boolean;
  tempDate?: Date | null;
  microchipNumber?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  vetPracticeId?: number;
  addedByReferral?: boolean;
  photoFileName?: string;
  photoFileType?: string;
  petPicture: string | null;
}

export interface AddPetInput {
  name: string;
  species: string;
  breed: string;
  sex: string;
  weight: number | string;
  weightUnit: string;
  dob: Date | null;
  photo?: string | null;
  microchipNumber?: string;
  insuranceProvider?: string;
  insurancePolicyNumber?: string;
  vetPracticeId?: number;
  addedByReferral?: boolean;
} 
export interface PetDetailsData {
  id: number;
  userId: number;
  name: string;
  species: string;
  breed: string;
  gender: string;
  weight: number;
  weightType: string;
  dateOfBirth: string;
  microchipNumber?: string | null;
  insuranceProvider?: string | null;
  insurancePolicyNumber?: string | null;
  vetPracticeId?: number | null;
  addedByReferral?: boolean;
  createdAt?: string;
  updatedAt?: string;
  petPicture?: string | null;
}
