import { getPets as fetchPetsApi } from '@/lib/vet/getPets';
import type { PetItemProps } from '@/components/ui/pet-item/services';
import { getS3SignedImageUrl } from '@/lib/vet247/documents';


// Match the actual API response
export interface PetData {
  id: number;
  userId: number;
  name: string;
  species: string;
  breed: string;
  gender: string;
  weight: number;
  weightType: string;
  dateOfBirth: string;
  microchipNumber?: string | null;
  insuranceProvider?: string | null;
  insurancePolicyNumber?: string | null;
  vetPracticeId?: number | null;
  addedByReferral?: boolean;
  createdAt?: string;
  updatedAt?: string;
  petPicture?: string | null;
}

export async function fetchPets(): Promise<PetItemProps[]> {
  const pets = await fetchPetsApi();
  return Promise.all(pets.map(mapPetToPetItemProps));
}

export async function mapPetToPetItemProps(pet: PetData): Promise<PetItemProps> {
  let image: any = require('@/assets/images/logo-green--no-text.png');
  if (pet.petPicture) {
    try {
      image = { uri: await getS3SignedImageUrl(pet.petPicture) };
    } catch (e) {
      // fallback to default image
    }
  }
  return {
    id: pet.id,
    image,
    name: pet.name,
    breed: `${pet.species} | ${pet.breed}`,
    sex: pet.gender,
    age: pet.dateOfBirth ? getAgeString(pet.dateOfBirth) : '',
    weight: pet.weight ? `${pet.weight} ${pet.weightType || ''}`.trim() : '',
  };
}

function getAgeString(dob: string | null): string {
  if (!dob) return '';
  const birthDate = new Date(dob);
  const now = new Date();
  let months = (now.getFullYear() - birthDate.getFullYear()) * 12;
  months -= birthDate.getMonth();
  months += now.getMonth();
  if (months < 12) return `${months} month${months !== 1 ? 's' : ''}`;
  const years = Math.floor(months / 12);
  return `${years} year${years !== 1 ? 's' : ''}`;
} 