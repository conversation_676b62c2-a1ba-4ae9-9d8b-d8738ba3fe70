import { getPetDetails as fetchPetDetailsApi } from '@/lib/vet/getPetDetails';
import { getS3SignedImageUrl } from '@/lib/vet247/documents';

// Match the actual API response
export interface PetDetailsData {
  id: number;
  userId: number;
  name: string;
  species: string;
  breed: string;
  gender: string;
  weight: number;
  weightType: string;
  dateOfBirth: string;
  microchipNumber?: string | null;
  insuranceProvider?: string | null;
  insurancePolicyNumber?: string | null;
  vetPracticeId?: number | null;
  addedByReferral?: boolean;
  createdAt?: string;
  updatedAt?: string;
  petPicture?: string | null;
}

export interface PetDetailsProps {
  id: number;
  name: string;
  breed: string;
  species: string;
  gender: string;
  weight: string;
  dateOfBirth: string;
  microchipNumber: string;
  insuranceProvider: string;
  insurancePolicyNumber: string;
  image: any;
}

export async function fetchPetDetails(petId: string | number): Promise<PetDetailsProps> {
  const petData = await fetchPetDetailsApi(petId);
  return mapPetDetailsToPetDetailsProps(petData);
}

export async function mapPetDetailsToPetDetailsProps(pet: PetDetailsData): Promise<PetDetailsProps> {
  let image: any = require('@/assets/images/logo-green--no-text.png');
  if (pet.petPicture) {
    try {
      image = { uri: await getS3SignedImageUrl(pet.petPicture) };
    } catch (e) {
      // fallback to default image
    }
  }

  return {
    id: pet.id,
    name: pet.name,
    breed: pet.breed,
    species: pet.species,
    gender: pet.gender,
    weight: pet.weight ? `${pet.weight} ${pet.weightType || ''}`.trim() : '--',
    dateOfBirth: pet.dateOfBirth ? formatDateOfBirth(pet.dateOfBirth) : '--',
    microchipNumber: pet.microchipNumber || '--',
    insuranceProvider: pet.insuranceProvider || '--',
    insurancePolicyNumber: pet.insurancePolicyNumber || '--',
    image,
  };
}

function formatDateOfBirth(dob: string): string {
  if (!dob) return '--';
  const date = new Date(dob);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
}