import { StyleSheet } from 'react-native';
import { COLOURS } from '../../../../constants/colours';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLOURS.white,
  },
  contentContainer: {
    alignItems: 'center',
    paddingBottom: 100,
    backgroundColor: COLOURS.grey
  },
  petImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 1.5,
    marginTop: 20,
    marginBottom: 8,
    borderColor: COLOURS.borderColor,
  },
  petName: {
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 4,
  },
  petBreed: {
    textAlign: 'center',
    marginBottom: 24,
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: COLOURS.borderColor,
    borderRadius: 26,
    marginBottom: 16,
    alignSelf: 'center',
    padding: 3,
    width: '90%',
  },
  tab: {
    paddingVertical: 10,
    paddingHorizontal: 3,
    borderRadius: 26,
    flex: 1,
    alignItems: 'center',
  },
  tabActive: {
    backgroundColor: COLOURS.white,
    shadowColor: COLOURS.black,
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
    shadowOffset: { width: 0, height: 1 },
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 8,
    backgroundColor: COLOURS.white,
  },
  outlineButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLOURS.primary,
    borderRadius: 30,
    paddingVertical: 12,
    marginRight: 10,
    alignItems: 'center',
    backgroundColor: COLOURS.white,
  },
  outlineButtonText: {
    letterSpacing: 0.8,
    textTransform: 'uppercase',
    width: '100%',
    textAlign: 'center',
  },
  filledButton: {
    flex: 1,
    backgroundColor: COLOURS.primary,
    borderRadius: 30,
    paddingVertical: 12,
    marginLeft: 10,
    alignItems: 'center',
  },
  filledButtonText: {
    letterSpacing: 0.8,
    textTransform: 'uppercase',
    width: '100%',
    textAlign: 'center',
  },
  loaderStyle: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default styles;